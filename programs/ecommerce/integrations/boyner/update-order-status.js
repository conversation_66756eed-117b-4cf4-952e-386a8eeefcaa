import {apiRequest} from './utils';

export default async function (app, store, packageId, status, trackingNumber = null) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        // Map internal status to Boyner status
        const boynerStatus = mapStatusToBoyner(status);

        if (!boynerStatus) {
            throw new Error(`Invalid status: ${status}`);
        }

        // Update package status
        const result = await apiRequest({
            method: 'put',
            url,
            path: `sapigw/suppliers/${supplierId}/shipment-packages/${packageId}/status/${boynerStatus}`,
            supplierId,
            apiKey,
            apiSecret
        });

        await log({
            level: 'info',
            message: `Package status updated successfully`,
            data: {packageId, status: boynerStatus, result}
        });

        // If tracking number is provided and status is shipped, update tracking number
        if (trackingNumber && boynerStatus === 'Shipped') {
            try {
                await apiRequest({
                    method: 'post',
                    url,
                    path: `sapigw/suppliers/${supplierId}/${packageId}/update-tracking-number`,
                    data: {trackingNumber},
                    supplierId,
                    apiKey,
                    apiSecret
                });

                await log({
                    level: 'info',
                    message: `Tracking number updated successfully`,
                    data: {packageId, trackingNumber}
                });
            } catch (trackingError) {
                await log({
                    level: 'error',
                    message: `Failed to update tracking number: ${trackingError.message}`,
                    data: {packageId, trackingNumber, error: trackingError.message}
                });
            }
        }

        return result;

    } catch (error) {
        await log({
            level: 'error',
            message: `Failed to update package status: ${error.message}`,
            data: {packageId, status, error: error.message}
        });
        throw error;
    }
}

function mapStatusToBoyner(status) {
    const statusMap = {
        'picking': 'Picking',
        'shipped': 'Shipped',
        'delivered': 'Delivered',
        'undelivered': 'UnDelivered',
        'cancelled': 'Cancelled'
    };
    
    return statusMap[status] || null;
}
